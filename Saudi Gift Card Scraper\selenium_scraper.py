from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import pandas as pd
import time
import random
import json
import os
import re

class SeleniumGiftCardScraper:
    def __init__(self):
        self.data = []
        self.setup_driver()
        os.makedirs('scraped_data', exist_ok=True)
        
    def setup_driver(self):
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
        
        # Add proxy
        proxy = "185.169.231.131:12323"
        chrome_options.add_argument(f'--proxy-server=http://14afa5bcaad0e:8218998f56@{proxy}')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
        except Exception as e:
            print(f"Error setting up Chrome driver: {e}")
            print("Trying without proxy...")
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
    
    def scrape_daleelstore(self):
        print("Scraping DaleelStore...")
        try:
            self.driver.get("https://www.daleelstore.com")
            time.sleep(3)
            
            # Look for gift card categories or products
            selectors = [
                "a[href*='gift']",
                "a[href*='card']",
                "a[href*='gaming']",
                ".product-item a",
                ".category a"
            ]
            
            links = []
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and href not in links:
                            links.append(href)
                except:
                    continue
            
            print(f"Found {len(links)} potential product links")
            
            for link in links[:10]:  # Limit for testing
                self.scrape_product_selenium(link, "DaleelStore")
                time.sleep(random.uniform(2, 4))
                
        except Exception as e:
            print(f"Error scraping DaleelStore: {e}")
    
    def scrape_product_selenium(self, url, platform):
        try:
            self.driver.get(url)
            time.sleep(2)
            
            product_data = {
                'platform': platform,
                'url': url,
                'card_name': '',
                'category': '',
                'country': 'Saudi Arabia',
                'denominations': [],
                'prices': [],
                'discount': '',
                'delivery_method': 'Digital',
                'description': ''
            }
            
            # Extract product name
            name_selectors = ['h1', '.product-title', '.product-name', '[class*="title"]']
            for selector in name_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    product_data['card_name'] = element.text.strip()
                    break
                except:
                    continue
            
            # Extract prices
            price_selectors = ['.price', '.product-price', '[class*="price"]', '[class*="cost"]']
            for selector in price_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        price_text = element.text.strip()
                        if price_text and any(char.isdigit() for char in price_text):
                            product_data['prices'].append(price_text)
                except:
                    continue
            
            # Extract description
            desc_selectors = ['.description', '.product-description', '.details', '.summary']
            for selector in desc_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    product_data['description'] = element.text.strip()[:200]
                    break
                except:
                    continue
            
            # Categorize
            name_lower = product_data['card_name'].lower()
            if any(word in name_lower for word in ['playstation', 'xbox', 'steam', 'gaming', 'game', 'pubg']):
                product_data['category'] = 'Gaming'
            elif any(word in name_lower for word in ['stc', 'mobily', 'zain', 'telecom', 'mobile', 'phone']):
                product_data['category'] = 'Telecom'
            elif any(word in name_lower for word in ['amazon', 'google', 'apple', 'shopping', 'store']):
                product_data['category'] = 'Retail'
            else:
                product_data['category'] = 'Other'
            
            if product_data['card_name']:
                self.data.append(product_data)
                print(f"Scraped: {product_data['card_name']}")
                
        except Exception as e:
            print(f"Error scraping product {url}: {e}")
    
    def scrape_all_platforms(self):
        platforms = [
            ("https://www.daleelstore.com", "DaleelStore"),
            ("https://www.like4card.com", "LikeCard"),
            ("https://www.bitaqaty.com", "Bitaqaty"),
            ("https://www.onecard.net", "OneCard"),
            ("https://www.rasseed.com", "Rasseed"),
            ("https://www.resal.me", "Resal")
        ]
        
        for base_url, platform_name in platforms:
            print(f"\n=== Scraping {platform_name} ===")
            try:
                self.scrape_platform_selenium(base_url, platform_name)
            except Exception as e:
                print(f"Error scraping {platform_name}: {str(e)}")
            
            time.sleep(random.uniform(3, 6))
    
    def scrape_platform_selenium(self, base_url, platform_name):
        try:
            self.driver.get(base_url)
            time.sleep(3)
            
            # Look for product links
            link_selectors = [
                "a[href*='product']",
                "a[href*='card']",
                "a[href*='gift']",
                ".product a",
                ".card a",
                ".item a"
            ]
            
            product_links = set()
            for selector in link_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        href = element.get_attribute('href')
                        if href and base_url in href:
                            product_links.add(href)
                except:
                    continue
            
            print(f"Found {len(product_links)} product links for {platform_name}")
            
            for i, link in enumerate(list(product_links)[:8]):  # Limit per platform
                print(f"Scraping product {i+1}: {link}")
                self.scrape_product_selenium(link, platform_name)
                time.sleep(random.uniform(2, 4))
                
        except Exception as e:
            print(f"Error scraping platform {platform_name}: {e}")
    
    def save_to_csv(self):
        if not self.data:
            print("No data to save")
            return
            
        csv_data = []
        for item in self.data:
            if item['prices']:
                for price in item['prices']:
                    csv_row = {
                        'Platform': item['platform'],
                        'Country': item['country'],
                        'Card Name': item['card_name'],
                        'Category': item['category'],
                        'Price/Denomination': price,
                        'Discount': item['discount'],
                        'Delivery Method': item['delivery_method'],
                        'Description': item['description'],
                        'URL': item['url']
                    }
                    csv_data.append(csv_row)
            else:
                csv_row = {
                    'Platform': item['platform'],
                    'Country': item['country'],
                    'Card Name': item['card_name'],
                    'Category': item['category'],
                    'Price/Denomination': 'N/A',
                    'Discount': item['discount'],
                    'Delivery Method': item['delivery_method'],
                    'Description': item['description'],
                    'URL': item['url']
                }
                csv_data.append(csv_row)
        
        df = pd.DataFrame(csv_data)
        filename = f"scraped_data/saudi_gift_cards_selenium_{int(time.time())}.csv"
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"Data saved to {filename}")
        print(f"Total records: {len(csv_data)}")
        
        return filename
    
    def close(self):
        if hasattr(self, 'driver'):
            self.driver.quit()

if __name__ == "__main__":
    scraper = SeleniumGiftCardScraper()
    try:
        scraper.scrape_all_platforms()
        scraper.save_to_csv()
    finally:
        scraper.close()
