import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import random
import csv
import json
import os
import re
from urllib.parse import urljoin, urlparse
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class TestGiftCardScraper:
    def __init__(self):
        self.data = []
        self.session = requests.Session()
        
        # Create output directory
        os.makedirs('scraped_data', exist_ok=True)
        
        # Headers to mimic a real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def get_page(self, url):
        """Fetch a webpage with error handling"""
        try:
            print(f"Fetching: {url}")
            response = self.session.get(
                url, 
                headers=self.headers, 
                timeout=30,
                verify=False
            )
            
            if response.status_code == 200:
                print(f"✓ Success: {response.status_code}")
                return response
            else:
                print(f"✗ HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"✗ Error: {str(e)}")
            return None
    
    def extract_product_info(self, soup, url, platform):
        """Extract product information from a page"""
        product = {
            'platform': platform,
            'url': url,
            'card_name': '',
            'category': '',
            'country': 'Saudi Arabia',
            'denominations': [],
            'prices': [],
            'discount': '',
            'delivery_method': 'Digital',
            'description': ''
        }
        
        # Extract product name
        name_selectors = [
            'h1',
            '.product-title',
            '.product-name',
            '.title',
            '[class*="title"]',
            '.name'
        ]
        
        for selector in name_selectors:
            element = soup.select_one(selector)
            if element and element.get_text().strip():
                product['card_name'] = element.get_text().strip()
                print(f"  Found name: {product['card_name']}")
                break
        
        # Extract prices
        price_selectors = [
            '.price',
            '.product-price',
            '[class*="price"]',
            '.cost',
            '[class*="cost"]',
            '.amount'
        ]
        
        for selector in price_selectors:
            elements = soup.select(selector)
            for element in elements:
                price_text = element.get_text().strip()
                # Look for numbers that might be prices
                if re.search(r'\d+', price_text):
                    product['prices'].append(price_text)
        
        if product['prices']:
            print(f"  Found prices: {product['prices'][:3]}...")  # Show first 3
        
        # Extract description
        desc_selectors = [
            '.description',
            '.product-description',
            '.details',
            '.summary',
            '.content'
        ]
        
        for selector in desc_selectors:
            element = soup.select_one(selector)
            if element:
                desc_text = element.get_text().strip()
                if len(desc_text) > 20:  # Only meaningful descriptions
                    product['description'] = desc_text[:200]
                    break
        
        # Categorize based on product name
        name_lower = product['card_name'].lower()
        if any(word in name_lower for word in ['playstation', 'xbox', 'steam', 'gaming', 'game', 'pubg', 'fortnite']):
            product['category'] = 'Gaming'
        elif any(word in name_lower for word in ['stc', 'mobily', 'zain', 'telecom', 'mobile', 'phone']):
            product['category'] = 'Telecom'
        elif any(word in name_lower for word in ['amazon', 'google', 'apple', 'shopping', 'store', 'itunes']):
            product['category'] = 'Retail'
        else:
            product['category'] = 'Other'
        
        return product
    
    def test_single_platform(self, base_url, platform_name):
        """Test scraping a single platform"""
        print(f"\n{'='*50}")
        print(f"Testing {platform_name}")
        print(f"{'='*50}")
        
        # Get the main page
        response = self.get_page(base_url)
        if not response:
            print(f"Failed to access {platform_name}")
            return
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find product/category links
        link_patterns = [
            'a[href*="product"]',
            'a[href*="card"]',
            'a[href*="gift"]',
            'a[href*="gaming"]',
            'a[href*="telecom"]',
            '.product a',
            '.card a',
            '.item a'
        ]
        
        product_links = set()
        for pattern in link_patterns:
            links = soup.select(pattern)
            for link in links:
                href = link.get('href')
                if href:
                    # Convert relative URLs to absolute
                    full_url = urljoin(base_url, href)
                    # Only include links from the same domain
                    if urlparse(full_url).netloc == urlparse(base_url).netloc:
                        product_links.add(full_url)
        
        print(f"Found {len(product_links)} potential product links")
        
        # Test first few links
        scraped_count = 0
        for i, link in enumerate(list(product_links)[:5]):  # Test only 5 links
            print(f"\n--- Testing link {i+1}: {link} ---")
            
            response = self.get_page(link)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')
                product = self.extract_product_info(soup, link, platform_name)
                
                if product['card_name']:  # Only add if we found a name
                    self.data.append(product)
                    scraped_count += 1
                    print(f"  ✓ Successfully scraped: {product['card_name']}")
                else:
                    print(f"  ✗ No product name found")
            
            # Be respectful with delays
            time.sleep(2)
        
        print(f"\nScraped {scraped_count} products from {platform_name}")
    
    def test_all_platforms(self):
        """Test scraping all platforms"""
        platforms = [
            ("https://www.daleelstore.com", "DaleelStore"),
            ("https://www.like4card.com", "LikeCard"),
            ("https://www.bitaqaty.com", "Bitaqaty"),
            ("https://www.onecard.net", "OneCard"),
            ("https://www.rasseed.com", "Rasseed"),
            ("https://www.resal.me", "Resal")
        ]
        
        for base_url, platform_name in platforms:
            try:
                self.test_single_platform(base_url, platform_name)
            except Exception as e:
                print(f"Error testing {platform_name}: {str(e)}")
            
            # Wait between platforms
            print(f"Waiting before next platform...")
            time.sleep(5)
    
    def save_to_csv(self):
        """Save scraped data to CSV"""
        if not self.data:
            print("No data to save")
            return None
        
        # Prepare data for CSV
        csv_data = []
        for item in self.data:
            if item['prices']:
                # Create a row for each price/denomination
                for price in item['prices']:
                    csv_row = {
                        'Platform': item['platform'],
                        'Country': item['country'],
                        'Card Name': item['card_name'],
                        'Category': item['category'],
                        'Price/Denomination': price,
                        'Discount': item['discount'],
                        'Delivery Method': item['delivery_method'],
                        'Description': item['description'],
                        'URL': item['url']
                    }
                    csv_data.append(csv_row)
            else:
                # No prices found
                csv_row = {
                    'Platform': item['platform'],
                    'Country': item['country'],
                    'Card Name': item['card_name'],
                    'Category': item['category'],
                    'Price/Denomination': 'N/A',
                    'Discount': item['discount'],
                    'Delivery Method': item['delivery_method'],
                    'Description': item['description'],
                    'URL': item['url']
                }
                csv_data.append(csv_row)
        
        # Save to CSV
        df = pd.DataFrame(csv_data)
        timestamp = int(time.time())
        filename = f"scraped_data/test_saudi_gift_cards_{timestamp}.csv"
        df.to_csv(filename, index=False, encoding='utf-8')
        
        print(f"\n{'='*50}")
        print(f"TEST SCRAPING COMPLETE!")
        print(f"{'='*50}")
        print(f"Data saved to: {filename}")
        print(f"Total records: {len(csv_data)}")
        print(f"Unique products: {len(self.data)}")
        
        # Show summary by platform
        platform_counts = df['Platform'].value_counts()
        print(f"\nProducts by platform:")
        for platform, count in platform_counts.items():
            print(f"  {platform}: {count}")
        
        return filename

if __name__ == "__main__":
    print("Starting test scraper...")
    scraper = TestGiftCardScraper()
    scraper.test_all_platforms()
    scraper.save_to_csv()
