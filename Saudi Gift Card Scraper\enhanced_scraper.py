import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import random
import csv
import json
import os
import re
from urllib.parse import urljoin, urlparse
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class EnhancedGiftCardScraper:
    def __init__(self):
        self.proxies = [
            {
                'http': '*****************************************************',
                'https': '*****************************************************'
            },
            {
                'http': '***************************************************',
                'https': '***************************************************'
            }
        ]
        self.current_proxy = 0
        self.data = []
        self.session = requests.Session()

        # Create output directory
        os.makedirs('scraped_data', exist_ok=True)

        # Headers to mimic a real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def get_page(self, url, use_proxy=True):
        """Fetch a webpage with error handling and proxy rotation"""
        for attempt in range(3):
            try:
                if use_proxy:
                    proxy = self.proxies[self.current_proxy]
                    self.current_proxy = (self.current_proxy + 1) % len(self.proxies)
                    print(f"  Using proxy: {proxy['http'].split('@')[1]}")
                else:
                    proxy = None

                response = self.session.get(
                    url,
                    headers=self.headers,
                    proxies=proxy,
                    timeout=30,
                    verify=False
                )

                if response.status_code == 200:
                    print(f"  ✓ Success: {response.status_code}")
                    return response
                elif response.status_code == 403:
                    print(f"  ✗ Blocked: {response.status_code}")
                    if not use_proxy:
                        return None
                else:
                    print(f"  ✗ HTTP {response.status_code}")

            except Exception as e:
                print(f"  ✗ Attempt {attempt + 1} failed: {str(e)}")
                if attempt < 2:
                    time.sleep(random.uniform(2, 5))

        # Try without proxy if all attempts failed
        if use_proxy:
            print(f"  Trying without proxy...")
            return self.get_page(url, use_proxy=False)

        return None

    def scrape_bitaqaty_detailed(self):
        """Detailed scraping for Bitaqaty"""
        print(f"\n{'='*50}")
        print(f"Detailed Bitaqaty Scraping")
        print(f"{'='*50}")

        base_url = "https://www.bitaqaty.com"

        # Try different category pages
        category_urls = [
            f"{base_url}/gaming/",
            f"{base_url}/telecom/",
            f"{base_url}/shopping/",
            f"{base_url}/entertainment/",
            f"{base_url}/new-cards/",
            f"{base_url}/cards/",
        ]

        for category_url in category_urls:
            print(f"\nScraping category: {category_url}")
            response = self.get_page(category_url)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Look for product cards
                product_selectors = [
                    '.card',
                    '.product',
                    '.item',
                    '[class*="card"]',
                    '[class*="product"]'
                ]

                for selector in product_selectors:
                    products = soup.select(selector)
                    for product in products[:10]:  # Limit per category
                        self.extract_bitaqaty_product(product, base_url)

            time.sleep(3)

    def extract_bitaqaty_product(self, product_element, base_url):
        """Extract product info from Bitaqaty product element"""
        try:
            product_data = {
                'platform': 'Bitaqaty',
                'url': '',
                'card_name': '',
                'category': '',
                'country': 'Saudi Arabia',
                'denominations': [],
                'prices': [],
                'discount': '',
                'delivery_method': 'Digital',
                'description': ''
            }

            # Extract product name
            name_element = product_element.select_one('h3, h4, .title, .name, [class*="title"], [class*="name"]')
            if name_element:
                product_data['card_name'] = name_element.get_text().strip()

            # Extract link
            link_element = product_element.select_one('a')
            if link_element and link_element.get('href'):
                product_data['url'] = urljoin(base_url, link_element.get('href'))

            # Extract price
            price_element = product_element.select_one('.price, [class*="price"], .cost, [class*="cost"]')
            if price_element:
                price_text = price_element.get_text().strip()
                if re.search(r'\d+', price_text):
                    product_data['prices'].append(price_text)

            # Categorize
            name_lower = product_data['card_name'].lower()
            if any(word in name_lower for word in ['playstation', 'xbox', 'steam', 'gaming', 'game', 'pubg']):
                product_data['category'] = 'Gaming'
            elif any(word in name_lower for word in ['stc', 'mobily', 'zain', 'telecom', 'mobile']):
                product_data['category'] = 'Telecom'
            elif any(word in name_lower for word in ['amazon', 'google', 'apple', 'shopping']):
                product_data['category'] = 'Retail'
            else:
                product_data['category'] = 'Other'

            if product_data['card_name']:
                self.data.append(product_data)
                print(f"    ✓ {product_data['card_name']}")

        except Exception as e:
            print(f"    ✗ Error extracting product: {e}")

    def scrape_onecard_detailed(self):
        """Detailed scraping for OneCard"""
        print(f"\n{'='*50}")
        print(f"Detailed OneCard Scraping")
        print(f"{'='*50}")

        base_url = "https://www.onecard.net"

        # Try different language/region combinations
        region_urls = [
            f"{base_url}/sa-ar/",
            f"{base_url}/sa-en/",
            f"{base_url}/",
        ]

        for region_url in region_urls:
            print(f"\nTrying region: {region_url}")
            response = self.get_page(region_url)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Look for category or product links
                links = soup.select('a[href*="card"], a[href*="product"], a[href*="gaming"], a[href*="telecom"]')

                for link in links[:15]:  # Limit
                    href = link.get('href')
                    if href:
                        full_url = urljoin(region_url, href)
                        print(f"  Checking: {full_url}")

                        response = self.get_page(full_url)
                        if response:
                            soup = BeautifulSoup(response.content, 'html.parser')
                            product = self.extract_generic_product(soup, full_url, "OneCard")
                            if product['card_name']:
                                self.data.append(product)
                                print(f"    ✓ {product['card_name']}")

                        time.sleep(2)

            time.sleep(3)

    def extract_generic_product(self, soup, url, platform):
        """Generic product extraction"""
        product = {
            'platform': platform,
            'url': url,
            'card_name': '',
            'category': '',
            'country': 'Saudi Arabia',
            'denominations': [],
            'prices': [],
            'discount': '',
            'delivery_method': 'Digital',
            'description': ''
        }

        # Extract product name
        name_selectors = ['h1', 'h2', '.product-title', '.title', '.name', '[class*="title"]']
        for selector in name_selectors:
            element = soup.select_one(selector)
            if element and element.get_text().strip():
                product['card_name'] = element.get_text().strip()
                break

        # Extract prices
        price_selectors = ['.price', '[class*="price"]', '.cost', '[class*="cost"]']
        for selector in price_selectors:
            elements = soup.select(selector)
            for element in elements:
                price_text = element.get_text().strip()
                if re.search(r'\d+', price_text):
                    product['prices'].append(price_text)

        # Categorize
        name_lower = product['card_name'].lower()
        if any(word in name_lower for word in ['playstation', 'xbox', 'steam', 'gaming', 'game']):
            product['category'] = 'Gaming'
        elif any(word in name_lower for word in ['stc', 'mobily', 'zain', 'telecom', 'mobile']):
            product['category'] = 'Telecom'
        elif any(word in name_lower for word in ['amazon', 'google', 'apple', 'shopping']):
            product['category'] = 'Retail'
        else:
            product['category'] = 'Other'

        return product

    def scrape_accessible_platforms(self):
        """Scrape platforms that are accessible"""
        # Based on test results, focus on accessible platforms
        self.scrape_bitaqaty_comprehensive()
        self.scrape_resal_comprehensive()
        self.scrape_rasseed_comprehensive()

        # Try other platforms with proxies
        other_platforms = [
            ("https://www.daleelstore.com", "DaleelStore"),
            ("https://www.like4card.com", "LikeCard"),
        ]

        for base_url, platform_name in other_platforms:
            print(f"\n{'='*50}")
            print(f"Trying {platform_name} with proxies")
            print(f"{'='*50}")

            response = self.get_page(base_url, use_proxy=True)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Look for product links
                links = soup.select('a[href*="product"], a[href*="card"], a[href*="gift"]')

                for link in links[:10]:  # Limit
                    href = link.get('href')
                    if href:
                        full_url = urljoin(base_url, href)
                        response = self.get_page(full_url, use_proxy=True)
                        if response:
                            soup = BeautifulSoup(response.content, 'html.parser')
                            product = self.extract_generic_product(soup, full_url, platform_name)
                            if product['card_name']:
                                self.data.append(product)
                                print(f"    ✓ {product['card_name']}")

                        time.sleep(3)

            time.sleep(5)

    def scrape_bitaqaty_comprehensive(self):
        """Comprehensive Bitaqaty scraping"""
        print(f"\n{'='*50}")
        print(f"Comprehensive Bitaqaty Scraping")
        print(f"{'='*50}")

        base_url = "https://www.bitaqaty.com"

        # Get main page and look for actual categories
        response = self.get_page(base_url)
        if response:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Look for navigation links, category links
            category_links = soup.select('a[href*="/"], nav a, .menu a, .category a')

            unique_urls = set()
            for link in category_links:
                href = link.get('href')
                if href and not any(skip in href for skip in ['javascript:', 'mailto:', '#', 'tel:']):
                    full_url = urljoin(base_url, href)
                    if urlparse(full_url).netloc == urlparse(base_url).netloc:
                        unique_urls.add(full_url)

            print(f"Found {len(unique_urls)} potential category URLs")

            for url in list(unique_urls)[:20]:  # Limit to avoid overwhelming
                print(f"\nChecking: {url}")
                response = self.get_page(url)
                if response:
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Look for product elements
                    products = soup.select('.card, .product, .item, [class*="card"], [class*="product"]')
                    for product in products[:5]:  # Limit per page
                        self.extract_bitaqaty_product(product, base_url)

                time.sleep(2)

    def scrape_resal_comprehensive(self):
        """Comprehensive Resal scraping"""
        print(f"\n{'='*50}")
        print(f"Comprehensive Resal Scraping")
        print(f"{'='*50}")

        base_url = "https://www.resal.me"

        # Try different endpoints
        endpoints = [
            f"{base_url}",
            f"{base_url}/cards",
            f"{base_url}/products",
            f"{base_url}/gaming",
            f"{base_url}/telecom",
            "https://citycards.resal.me"
        ]

        for endpoint in endpoints:
            print(f"\nChecking endpoint: {endpoint}")
            response = self.get_page(endpoint, use_proxy=True)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')

                # Extract products from this page
                products = soup.select('.card, .product, .item, [class*="card"], [class*="product"]')
                for product in products[:10]:
                    self.extract_resal_product(product, base_url)

                # Look for more product links
                links = soup.select('a[href*="card"], a[href*="product"]')
                for link in links[:10]:
                    href = link.get('href')
                    if href:
                        full_url = urljoin(endpoint, href)
                        response = self.get_page(full_url, use_proxy=True)
                        if response:
                            soup = BeautifulSoup(response.content, 'html.parser')
                            product = self.extract_generic_product(soup, full_url, "Resal")
                            if product['card_name']:
                                self.data.append(product)
                                print(f"    ✓ {product['card_name']}")
                        time.sleep(2)

            time.sleep(3)

    def scrape_rasseed_comprehensive(self):
        """Comprehensive Rasseed scraping"""
        print(f"\n{'='*50}")
        print(f"Comprehensive Rasseed Scraping")
        print(f"{'='*50}")

        base_url = "https://www.rasseed.com"

        response = self.get_page(base_url, use_proxy=True)
        if response:
            soup = BeautifulSoup(response.content, 'html.parser')

            # Look for product/category links
            links = soup.select('a')

            product_urls = set()
            for link in links:
                href = link.get('href')
                if href and any(keyword in href.lower() for keyword in ['card', 'product', 'gaming', 'telecom']):
                    full_url = urljoin(base_url, href)
                    if urlparse(full_url).netloc == urlparse(base_url).netloc:
                        product_urls.add(full_url)

            print(f"Found {len(product_urls)} potential product URLs")

            for url in list(product_urls)[:15]:
                print(f"\nChecking: {url}")
                response = self.get_page(url, use_proxy=True)
                if response:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    product = self.extract_generic_product(soup, url, "Rasseed")
                    if product['card_name']:
                        self.data.append(product)
                        print(f"    ✓ {product['card_name']}")

                time.sleep(3)

    def extract_resal_product(self, product_element, base_url):
        """Extract product info from Resal product element"""
        try:
            product_data = {
                'platform': 'Resal',
                'url': '',
                'card_name': '',
                'category': '',
                'country': 'Saudi Arabia',
                'denominations': [],
                'prices': [],
                'discount': '',
                'delivery_method': 'Digital',
                'description': ''
            }

            # Extract product name
            name_element = product_element.select_one('h1, h2, h3, h4, .title, .name, [class*="title"], [class*="name"]')
            if name_element:
                product_data['card_name'] = name_element.get_text().strip()

            # Extract link
            link_element = product_element.select_one('a')
            if link_element and link_element.get('href'):
                product_data['url'] = urljoin(base_url, link_element.get('href'))

            # Extract price
            price_element = product_element.select_one('.price, [class*="price"], .cost, [class*="cost"]')
            if price_element:
                price_text = price_element.get_text().strip()
                if re.search(r'\d+', price_text):
                    product_data['prices'].append(price_text)

            # Categorize
            name_lower = product_data['card_name'].lower()
            if any(word in name_lower for word in ['playstation', 'xbox', 'steam', 'gaming', 'game']):
                product_data['category'] = 'Gaming'
            elif any(word in name_lower for word in ['stc', 'mobily', 'zain', 'telecom', 'mobile']):
                product_data['category'] = 'Telecom'
            elif any(word in name_lower for word in ['amazon', 'google', 'apple', 'shopping']):
                product_data['category'] = 'Retail'
            else:
                product_data['category'] = 'Other'

            if product_data['card_name']:
                self.data.append(product_data)
                print(f"    ✓ {product_data['card_name']}")

        except Exception as e:
            print(f"    ✗ Error extracting Resal product: {e}")

    def save_to_csv(self):
        """Save scraped data to CSV"""
        if not self.data:
            print("No data to save")
            return None

        # Prepare data for CSV
        csv_data = []
        for item in self.data:
            if item['prices']:
                for price in item['prices']:
                    csv_row = {
                        'Platform': item['platform'],
                        'Country': item['country'],
                        'Card Name': item['card_name'],
                        'Category': item['category'],
                        'Price/Denomination': price,
                        'Discount': item['discount'],
                        'Delivery Method': item['delivery_method'],
                        'Description': item['description'],
                        'URL': item['url']
                    }
                    csv_data.append(csv_row)
            else:
                csv_row = {
                    'Platform': item['platform'],
                    'Country': item['country'],
                    'Card Name': item['card_name'],
                    'Category': item['category'],
                    'Price/Denomination': 'N/A',
                    'Discount': item['discount'],
                    'Delivery Method': item['delivery_method'],
                    'Description': item['description'],
                    'URL': item['url']
                }
                csv_data.append(csv_row)

        # Save to CSV
        df = pd.DataFrame(csv_data)
        timestamp = int(time.time())
        filename = f"scraped_data/enhanced_saudi_gift_cards_{timestamp}.csv"
        df.to_csv(filename, index=False, encoding='utf-8')

        print(f"\n{'='*50}")
        print(f"ENHANCED SCRAPING COMPLETE!")
        print(f"{'='*50}")
        print(f"Data saved to: {filename}")
        print(f"Total records: {len(csv_data)}")
        print(f"Unique products: {len(self.data)}")

        # Show summary by platform
        platform_counts = df['Platform'].value_counts()
        print(f"\nProducts by platform:")
        for platform, count in platform_counts.items():
            print(f"  {platform}: {count}")

        # Show summary by category
        category_counts = df['Category'].value_counts()
        print(f"\nProducts by category:")
        for category, count in category_counts.items():
            print(f"  {category}: {count}")

        return filename

if __name__ == "__main__":
    print("Starting enhanced scraper with proxy support...")
    scraper = EnhancedGiftCardScraper()
    scraper.scrape_accessible_platforms()
    scraper.save_to_csv()
