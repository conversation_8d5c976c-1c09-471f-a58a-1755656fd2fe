# Saudi Arabia Gift Card Market Scraper

A comprehensive web scraping solution for collecting digital gift card data from major Saudi Arabian platforms.

## 🎯 Project Objective

Analyze the Saudi Arabian digital gift card market by extracting product-level data from the top 6 platforms selling gaming, telecom, and shopping cards.

## 🏪 Target Platforms

| Platform | URL | Status | Data Quality |
|----------|-----|--------|--------------|
| DaleelStore | https://www.daleelstore.com | ❌ Blocked | N/A |
| LikeCard | https://www.like4card.com | ❌ Blocked | N/A |
| Bitaqaty | https://www.bitaqaty.com | ✅ Accessible | Limited |
| OneCard | https://www.onecard.net | ⚠️ Partial | Basic |
| Rasseed | https://www.rasseed.com | ✅ Full Access | Excellent |
| Resal | https://www.resal.me | ✅ Accessible | Good |

## 📊 Results Summary

- **154 total records** collected
- **77 unique products** identified
- **115 records with pricing** (75% coverage)
- **4 categories**: Gaming (40), Retail (38), Other (47), Telecom (29)

## 🛠️ Technical Stack

- **Python 3.13**
- **BeautifulSoup4** - HTML parsing
- **Requests** - HTTP client with proxy support
- **Pandas** - Data processing and export
- **Selenium** - Browser automation (alternative approach)

## 🔧 Installation

```bash
# Clone or download the project
cd "Saudi Gift Card Scraper"

# Install dependencies
pip install -r requirements.txt
```

## 🚀 Usage

### Quick Start
```bash
# Run the comprehensive scraper
python final_comprehensive_scraper.py
```

### Available Scrapers

1. **final_comprehensive_scraper.py** - Main production scraper
   - Full proxy support
   - Advanced price extraction
   - Comprehensive categorization
   - Best results

2. **enhanced_scraper.py** - Enhanced version with detailed platform handling
   - Platform-specific scraping logic
   - Proxy rotation
   - Good for targeted scraping

3. **test_scraper.py** - Simple test version
   - No proxy requirements
   - Quick testing
   - Basic functionality

4. **simple_scraper.py** - Basic scraper with proxy support
   - Straightforward implementation
   - Good starting point

## 📁 Output Files

All scraped data is saved in the `scraped_data/` directory:

- `final_saudi_gift_cards_[timestamp].csv` - Main dataset (154 records)
- `enhanced_saudi_gift_cards_[timestamp].csv` - Enhanced version (20 records)
- `test_saudi_gift_cards_[timestamp].csv` - Test results (1 record)

## 📋 Data Schema

| Column | Description | Example |
|--------|-------------|---------|
| Platform | Source platform name | "Rasseed" |
| Country | Target country | "Saudi Arabia" |
| Card Name | Product name | "بطاقة جواكر - 70000 توكنز" |
| Category | Product category | "Gaming" |
| Price/Denomination | Price or value | "70000 SAR" |
| Discount | Any discount info | "" |
| Delivery Method | How card is delivered | "Digital" |
| Description | Product description | "اذهب إلى صفحة استرداد..." |
| URL | Product page URL | "https://www.rasseed.com/..." |

## 🌐 Proxy Configuration

The scraper uses US-based proxies to bypass geo-restrictions:

```python
proxies = [
    {
        'http': '*****************************************************',
        'https': '*****************************************************'
    },
    {
        'http': '***************************************************',
        'https': '***************************************************'
    }
]
```

## 🎮 Key Product Categories

### Gaming Cards
- **Jawaker**: Popular card game platform (4,250 - 805,000 tokens)
- **R2 Games**: International gaming currency
- **PUBG New State**: Battle royale game currency
- **Heroes Evolved**: MOBA game tokens
- **LiveMe**: Social gaming platform

### Retail Gift Cards
- **Nana**: Food delivery service (50-1,000 SAR)
- **AlSaif Gallery**: Home goods retailer
- **Alshaya**: Fashion and lifestyle brands

### Telecom Cards
- **Salam**: Mobile recharge cards (20-100 SAR)
- **STC/Mobily**: Major telecom operators

## 💰 Price Analysis

### Common Denominations (SAR)
- **20**: Entry-level mobile recharge
- **50**: Standard gift card
- **100**: Popular retail amount
- **500**: Premium tier
- **1,000**: High-value cards

## 🔍 Market Insights

1. **Gaming dominates** the digital gift card market
2. **Mobile recharge** remains essential
3. **Local brands** (Nana, AlSaif) have strong presence
4. **Token-based** gaming economies are popular
5. **Digital delivery** is standard

## ⚠️ Challenges & Solutions

| Challenge | Solution |
|-----------|----------|
| Geo-blocking | US proxy servers |
| Rate limiting | Random delays (2-4s) |
| Dynamic content | Multiple scraping strategies |
| Arabic content | UTF-8 encoding |
| Site blocking | Proxy rotation |

## 📈 Future Enhancements

1. **Real-time monitoring** - Track price changes
2. **Deeper product data** - Extract more details from accessible platforms
3. **API integration** - Direct platform APIs where available
4. **Automated scheduling** - Daily/weekly scraping
5. **Data visualization** - Market analysis dashboard

## 📄 Documentation

- `SCRAPING_SUMMARY.md` - Detailed analysis and findings
- `README.md` - This file
- Code comments - Inline documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## ⚖️ Legal Notice

This scraper is designed for market research purposes. Please ensure compliance with:
- Platform Terms of Service
- Local data protection laws
- Respectful scraping practices (delays, rate limits)

## 📞 Support

For questions or issues:
1. Check the documentation
2. Review the code comments
3. Test with the simple scraper first
4. Verify proxy connectivity

---

**Last Updated**: January 2025  
**Success Rate**: 50% of target platforms accessible  
**Total Execution Time**: ~45 minutes for full scraping
