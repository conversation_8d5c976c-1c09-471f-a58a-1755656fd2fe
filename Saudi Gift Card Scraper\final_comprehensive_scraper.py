import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import random
import csv
import json
import os
import re
from urllib.parse import urljoin, urlparse
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class FinalGiftCardScraper:
    def __init__(self):
        self.proxies = [
            {
                'http': '*****************************************************',
                'https': '*****************************************************'
            },
            {
                'http': '***************************************************',
                'https': '***************************************************'
            }
        ]
        self.current_proxy = 0
        self.data = []
        self.session = requests.Session()
        
        # Create output directory
        os.makedirs('scraped_data', exist_ok=True)
        
        # Headers to mimic a real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def get_page(self, url, use_proxy=True):
        """Fetch a webpage with error handling and proxy rotation"""
        for attempt in range(3):
            try:
                if use_proxy:
                    proxy = self.proxies[self.current_proxy]
                    self.current_proxy = (self.current_proxy + 1) % len(self.proxies)
                else:
                    proxy = None
                
                response = self.session.get(
                    url, 
                    headers=self.headers, 
                    proxies=proxy,
                    timeout=30,
                    verify=False
                )
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 403:
                    if not use_proxy:
                        return None
                else:
                    print(f"  HTTP {response.status_code} for {url}")
                    
            except Exception as e:
                if attempt < 2:
                    time.sleep(random.uniform(1, 3))
                
        # Try without proxy if all attempts failed
        if use_proxy:
            return self.get_page(url, use_proxy=False)
        
        return None
    
    def categorize_product(self, name, description=""):
        """Categorize product based on name and description"""
        text = (name + " " + description).lower()
        
        # Gaming keywords
        gaming_keywords = [
            'playstation', 'xbox', 'steam', 'gaming', 'game', 'pubg', 'fortnite',
            'جواكر', 'jawaker', 'تيم فايت', 'teamfight', 'ار تو', 'r2', 'كوينز', 'coins',
            'توكنز', 'token', 'ريوت', 'riot', 'لول', 'lol', 'فالورانت', 'valorant'
        ]
        
        # Telecom keywords
        telecom_keywords = [
            'stc', 'mobily', 'zain', 'telecom', 'mobile', 'phone', 'recharge',
            'سلام', 'salam', 'شحن', 'إعادة شحن', 'موبايلي', 'زين'
        ]
        
        # Retail keywords
        retail_keywords = [
            'amazon', 'google', 'apple', 'shopping', 'store', 'itunes', 'gift card',
            'نعناع', 'nana', 'السيف', 'alsaif', 'الشايع', 'alshaya', 'قسيمة شراء',
            'gift', 'shopping', 'متجر'
        ]
        
        if any(keyword in text for keyword in gaming_keywords):
            return 'Gaming'
        elif any(keyword in text for keyword in telecom_keywords):
            return 'Telecom'
        elif any(keyword in text for keyword in retail_keywords):
            return 'Retail'
        else:
            return 'Other'
    
    def extract_price_from_text(self, text):
        """Extract price information from text"""
        prices = []
        
        # Look for SAR prices
        sar_pattern = r'(\d+(?:\.\d+)?)\s*(?:sar|ريال|sr)'
        sar_matches = re.findall(sar_pattern, text.lower())
        for match in sar_matches:
            prices.append(f"{match} SAR")
        
        # Look for USD prices
        usd_pattern = r'\$(\d+(?:\.\d+)?)'
        usd_matches = re.findall(usd_pattern, text)
        for match in usd_matches:
            prices.append(f"${match}")
        
        # Look for general numbers that might be prices
        if not prices:
            number_pattern = r'\b(\d+(?:\.\d+)?)\b'
            numbers = re.findall(number_pattern, text)
            for num in numbers[:3]:  # Take first 3 numbers
                if float(num) > 5 and float(num) < 10000:  # Reasonable price range
                    prices.append(num)
        
        return prices
    
    def scrape_rasseed_detailed(self):
        """Detailed Rasseed scraping with price extraction"""
        print(f"\n{'='*60}")
        print(f"DETAILED RASSEED SCRAPING")
        print(f"{'='*60}")
        
        base_url = "https://www.rasseed.com"
        
        # Get main page to find all product categories
        response = self.get_page(base_url, use_proxy=True)
        if not response:
            print("Failed to access Rasseed main page")
            return
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Find all product links
        product_links = set()
        for link in soup.select('a[href*="/ar/"]'):
            href = link.get('href')
            if href and any(keyword in href.lower() for keyword in ['card', 'gift', 'recharge', 'token', 'coins']):
                full_url = urljoin(base_url, href)
                if urlparse(full_url).netloc == urlparse(base_url).netloc:
                    product_links.add(full_url)
        
        print(f"Found {len(product_links)} product URLs")
        
        # Scrape each product page
        for i, url in enumerate(list(product_links)[:30]):  # Limit to 30 products
            print(f"\n[{i+1}/{min(30, len(product_links))}] Scraping: {url}")
            
            response = self.get_page(url, use_proxy=True)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                product_data = {
                    'platform': 'Rasseed',
                    'url': url,
                    'card_name': '',
                    'category': '',
                    'country': 'Saudi Arabia',
                    'denominations': [],
                    'prices': [],
                    'discount': '',
                    'delivery_method': 'Digital',
                    'description': ''
                }
                
                # Extract product name
                name_selectors = ['h1', 'h2', '.product-title', '.title', '[class*="title"]']
                for selector in name_selectors:
                    element = soup.select_one(selector)
                    if element and element.get_text().strip():
                        product_data['card_name'] = element.get_text().strip()
                        break
                
                # Extract description
                desc_selectors = ['.description', '.product-description', '.details', 'p']
                for selector in desc_selectors:
                    element = soup.select_one(selector)
                    if element:
                        desc_text = element.get_text().strip()
                        if len(desc_text) > 20:
                            product_data['description'] = desc_text[:300]
                            break
                
                # Extract prices from page content
                page_text = soup.get_text()
                prices = self.extract_price_from_text(page_text)
                product_data['prices'] = prices
                
                # Extract price from URL (common pattern in Rasseed)
                url_price_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:%20|SAR|ريال)', url)
                if url_price_match:
                    url_price = url_price_match.group(1)
                    if url_price not in str(product_data['prices']):
                        product_data['prices'].append(f"{url_price} SAR")
                
                # Categorize
                product_data['category'] = self.categorize_product(
                    product_data['card_name'], 
                    product_data['description']
                )
                
                if product_data['card_name']:
                    self.data.append(product_data)
                    print(f"  ✓ {product_data['card_name']}")
                    print(f"    Category: {product_data['category']}")
                    if product_data['prices']:
                        print(f"    Prices: {product_data['prices'][:3]}")
                else:
                    print(f"  ✗ No product name found")
            
            time.sleep(random.uniform(2, 4))
    
    def scrape_bitaqaty_detailed(self):
        """Detailed Bitaqaty scraping"""
        print(f"\n{'='*60}")
        print(f"DETAILED BITAQATY SCRAPING")
        print(f"{'='*60}")
        
        base_url = "https://www.bitaqaty.com"
        
        # Try to access main page and find product categories
        response = self.get_page(base_url)
        if response:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for any links that might lead to products
            all_links = soup.select('a[href]')
            product_urls = set()
            
            for link in all_links:
                href = link.get('href')
                if href:
                    full_url = urljoin(base_url, href)
                    if urlparse(full_url).netloc == urlparse(base_url).netloc:
                        product_urls.add(full_url)
            
            print(f"Found {len(product_urls)} URLs to check")
            
            # Check each URL for products
            for i, url in enumerate(list(product_urls)[:20]):
                print(f"\n[{i+1}/20] Checking: {url}")
                
                response = self.get_page(url)
                if response:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Look for product information
                    title_element = soup.select_one('h1, h2, title')
                    if title_element:
                        title = title_element.get_text().strip()
                        
                        # Skip non-product pages
                        if any(skip in title.lower() for skip in ['about', 'contact', 'terms', 'policy', 'faq']):
                            continue
                        
                        product_data = {
                            'platform': 'Bitaqaty',
                            'url': url,
                            'card_name': title,
                            'category': self.categorize_product(title),
                            'country': 'Saudi Arabia',
                            'denominations': [],
                            'prices': self.extract_price_from_text(soup.get_text()),
                            'discount': '',
                            'delivery_method': 'Digital',
                            'description': ''
                        }
                        
                        if len(title) > 5 and title not in [item['card_name'] for item in self.data]:
                            self.data.append(product_data)
                            print(f"  ✓ {title}")
                
                time.sleep(2)
    
    def run_comprehensive_scraping(self):
        """Run comprehensive scraping of all accessible platforms"""
        print("Starting comprehensive gift card scraping...")
        print("Target platforms: Rasseed, Bitaqaty, Resal")
        
        # Scrape each platform
        self.scrape_rasseed_detailed()
        self.scrape_bitaqaty_detailed()
        
        # Quick scrape of Resal
        print(f"\n{'='*60}")
        print(f"QUICK RESAL SCRAPING")
        print(f"{'='*60}")
        
        resal_urls = [
            "https://www.resal.me",
            "https://citycards.resal.me",
            "https://giftcards.resal.me"
        ]
        
        for url in resal_urls:
            response = self.get_page(url, use_proxy=True)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract any product information
                for element in soup.select('h1, h2, h3, .title, .card-title'):
                    title = element.get_text().strip()
                    if len(title) > 5:
                        product_data = {
                            'platform': 'Resal',
                            'url': url,
                            'card_name': title,
                            'category': self.categorize_product(title),
                            'country': 'Saudi Arabia',
                            'denominations': [],
                            'prices': [],
                            'discount': '',
                            'delivery_method': 'Digital',
                            'description': ''
                        }
                        
                        if title not in [item['card_name'] for item in self.data]:
                            self.data.append(product_data)
                            print(f"  ✓ {title}")
            
            time.sleep(3)
    
    def save_to_csv(self):
        """Save scraped data to CSV with enhanced formatting"""
        if not self.data:
            print("No data to save")
            return None
        
        # Prepare data for CSV
        csv_data = []
        for item in self.data:
            if item['prices']:
                for price in item['prices']:
                    csv_row = {
                        'Platform': item['platform'],
                        'Country': item['country'],
                        'Card Name': item['card_name'],
                        'Category': item['category'],
                        'Price/Denomination': price,
                        'Discount': item['discount'],
                        'Delivery Method': item['delivery_method'],
                        'Description': item['description'][:100] + "..." if len(item['description']) > 100 else item['description'],
                        'URL': item['url']
                    }
                    csv_data.append(csv_row)
            else:
                csv_row = {
                    'Platform': item['platform'],
                    'Country': item['country'],
                    'Card Name': item['card_name'],
                    'Category': item['category'],
                    'Price/Denomination': 'N/A',
                    'Discount': item['discount'],
                    'Delivery Method': item['delivery_method'],
                    'Description': item['description'][:100] + "..." if len(item['description']) > 100 else item['description'],
                    'URL': item['url']
                }
                csv_data.append(csv_row)
        
        # Save to CSV
        df = pd.DataFrame(csv_data)
        timestamp = int(time.time())
        filename = f"scraped_data/final_saudi_gift_cards_{timestamp}.csv"
        df.to_csv(filename, index=False, encoding='utf-8')
        
        print(f"\n{'='*60}")
        print(f"FINAL SCRAPING COMPLETE!")
        print(f"{'='*60}")
        print(f"Data saved to: {filename}")
        print(f"Total records: {len(csv_data)}")
        print(f"Unique products: {len(self.data)}")
        
        # Show detailed summary
        platform_counts = df['Platform'].value_counts()
        print(f"\nProducts by platform:")
        for platform, count in platform_counts.items():
            print(f"  {platform}: {count}")
        
        category_counts = df['Category'].value_counts()
        print(f"\nProducts by category:")
        for category, count in category_counts.items():
            print(f"  {category}: {count}")
        
        # Show price statistics
        priced_items = df[df['Price/Denomination'] != 'N/A']
        print(f"\nPricing information:")
        print(f"  Items with prices: {len(priced_items)}")
        print(f"  Items without prices: {len(df) - len(priced_items)}")
        
        return filename

if __name__ == "__main__":
    scraper = FinalGiftCardScraper()
    scraper.run_comprehensive_scraping()
    scraper.save_to_csv()
