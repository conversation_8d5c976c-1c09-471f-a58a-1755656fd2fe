import requests
import pandas as pd
from bs4 import BeautifulSoup
import time
import random
import csv
import json
import os
from urllib.parse import urljoin, urlparse
from fake_useragent import UserAgent

class GiftCardScraper:
    def __init__(self):
        self.ua = UserAgent()
        self.proxies = [
            {
                'http': '*****************************************************',
                'https': '*****************************************************'
            },
            {
                'http': '***************************************************',
                'https': '***************************************************'
            }
        ]
        self.current_proxy_index = 0
        self.session = requests.Session()
        self.data = []
        
        # Create output directory
        os.makedirs('scraped_data', exist_ok=True)
        
    def get_headers(self):
        return {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def rotate_proxy(self):
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxies)
        return self.proxies[self.current_proxy_index]
    
    def make_request(self, url, max_retries=3):
        for attempt in range(max_retries):
            try:
                proxy = self.rotate_proxy()
                headers = self.get_headers()
                
                response = self.session.get(
                    url, 
                    headers=headers, 
                    proxies=proxy, 
                    timeout=30,
                    verify=False
                )
                
                if response.status_code == 200:
                    return response
                else:
                    print(f"Status code {response.status_code} for {url}")
                    
            except Exception as e:
                print(f"Attempt {attempt + 1} failed for {url}: {str(e)}")
                time.sleep(random.uniform(2, 5))
                
        return None
    
    def scrape_daleelstore(self):
        print("Scraping DaleelStore...")
        base_url = "https://www.daleelstore.com"
        
        # Try to find gift card categories
        response = self.make_request(base_url)
        if not response:
            return
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for gift card links and categories
        gift_card_links = []
        
        # Common selectors for gift card products
        selectors = [
            'a[href*="gift"]',
            'a[href*="card"]',
            'a[href*="gaming"]',
            'a[href*="telecom"]',
            '.product-item a',
            '.category-item a'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href')
                if href:
                    full_url = urljoin(base_url, href)
                    gift_card_links.append(full_url)
        
        # Remove duplicates
        gift_card_links = list(set(gift_card_links))
        
        for link in gift_card_links[:20]:  # Limit for testing
            self.scrape_product_page(link, "DaleelStore")
            time.sleep(random.uniform(1, 3))
    
    def scrape_product_page(self, url, platform):
        response = self.make_request(url)
        if not response:
            return
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract product information
        product_data = {
            'platform': platform,
            'url': url,
            'card_name': '',
            'category': '',
            'country': 'Saudi Arabia',  # Default
            'denominations': [],
            'prices': [],
            'discount': '',
            'delivery_method': '',
            'description': ''
        }
        
        # Try to extract product name
        name_selectors = ['h1', '.product-title', '.product-name', 'title']
        for selector in name_selectors:
            element = soup.select_one(selector)
            if element:
                product_data['card_name'] = element.get_text().strip()
                break
        
        # Try to extract price information
        price_selectors = ['.price', '.product-price', '[class*="price"]']
        for selector in price_selectors:
            elements = soup.select(selector)
            for element in elements:
                price_text = element.get_text().strip()
                if price_text:
                    product_data['prices'].append(price_text)
        
        # Try to extract description
        desc_selectors = ['.description', '.product-description', '.details']
        for selector in desc_selectors:
            element = soup.select_one(selector)
            if element:
                product_data['description'] = element.get_text().strip()[:200]
                break
        
        # Determine category based on product name
        name_lower = product_data['card_name'].lower()
        if any(word in name_lower for word in ['playstation', 'xbox', 'steam', 'gaming', 'game']):
            product_data['category'] = 'Gaming'
        elif any(word in name_lower for word in ['stc', 'mobily', 'zain', 'telecom', 'mobile']):
            product_data['category'] = 'Telecom'
        elif any(word in name_lower for word in ['amazon', 'google', 'apple', 'shopping']):
            product_data['category'] = 'Retail'
        else:
            product_data['category'] = 'Other'
        
        if product_data['card_name']:  # Only add if we found a name
            self.data.append(product_data)
            print(f"Scraped: {product_data['card_name']}")
    
    def scrape_all_platforms(self):
        platforms = [
            ("https://www.daleelstore.com", "DaleelStore"),
            ("https://www.like4card.com", "LikeCard"),
            ("https://www.bitaqaty.com", "Bitaqaty"),
            ("https://www.onecard.net", "OneCard"),
            ("https://www.rasseed.com", "Rasseed"),
            ("https://www.resal.me", "Resal")
        ]
        
        for base_url, platform_name in platforms:
            print(f"\n=== Scraping {platform_name} ===")
            try:
                self.scrape_platform(base_url, platform_name)
            except Exception as e:
                print(f"Error scraping {platform_name}: {str(e)}")
            
            # Wait between platforms
            time.sleep(random.uniform(5, 10))
    
    def scrape_platform(self, base_url, platform_name):
        response = self.make_request(base_url)
        if not response:
            print(f"Failed to access {platform_name}")
            return
            
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Look for product/category links
        product_links = set()
        
        # Common patterns for gift card sites
        link_patterns = [
            'a[href*="product"]',
            'a[href*="card"]',
            'a[href*="gift"]',
            'a[href*="gaming"]',
            'a[href*="telecom"]',
            '.product a',
            '.card a',
            '.item a'
        ]
        
        for pattern in link_patterns:
            links = soup.select(pattern)
            for link in links:
                href = link.get('href')
                if href:
                    full_url = urljoin(base_url, href)
                    product_links.add(full_url)
        
        # Scrape individual product pages
        for i, link in enumerate(list(product_links)[:15]):  # Limit for efficiency
            print(f"Scraping product {i+1}: {link}")
            self.scrape_product_page(link, platform_name)
            time.sleep(random.uniform(2, 4))
    
    def save_to_csv(self):
        if not self.data:
            print("No data to save")
            return
            
        # Flatten the data for CSV
        csv_data = []
        for item in self.data:
            if item['prices']:
                for price in item['prices']:
                    csv_row = {
                        'Platform': item['platform'],
                        'Country': item['country'],
                        'Card Name': item['card_name'],
                        'Category': item['category'],
                        'Price/Denomination': price,
                        'Discount': item['discount'],
                        'Delivery Method': item['delivery_method'],
                        'Description': item['description'],
                        'URL': item['url']
                    }
                    csv_data.append(csv_row)
            else:
                csv_row = {
                    'Platform': item['platform'],
                    'Country': item['country'],
                    'Card Name': item['card_name'],
                    'Category': item['category'],
                    'Price/Denomination': '',
                    'Discount': item['discount'],
                    'Delivery Method': item['delivery_method'],
                    'Description': item['description'],
                    'URL': item['url']
                }
                csv_data.append(csv_row)
        
        # Save to CSV
        df = pd.DataFrame(csv_data)
        filename = f"scraped_data/saudi_gift_cards_{int(time.time())}.csv"
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"Data saved to {filename}")
        print(f"Total records: {len(csv_data)}")
        
        return filename

if __name__ == "__main__":
    scraper = GiftCardScraper()
    scraper.scrape_all_platforms()
    scraper.save_to_csv()
